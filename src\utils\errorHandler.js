/**
 * 统一的错误处理工具
 */

/**
 * 处理API请求错误
 * @param {Error} error - 错误对象
 * @param {Object} context - Vue组件上下文
 * @param {string} defaultMessage - 默认错误消息
 */
export function handleApiError(error, context, defaultMessage = '请求失败') {
  console.error('API请求错误:', error);
  let errorMessage = defaultMessage;
  // 网络连接错误
  if (error.code === 'ECONNRESET' || error.message?.includes('ECONNRESET')) {
    errorMessage = '无法连接到后端服务器，请检查网络连接或联系管理员';
  } else if (error.response?.status === 401) { // 认证错误
    errorMessage = '认证失败，请重新登录';
    // 可以在这里触发重新登录逻辑
    // context.$router.push('/login');
  } else if (error.response?.status === 403) { // 权限错误
    errorMessage = '权限不足，无法访问该资源';
  } else if (error.response?.status >= 500) { // 服务器错误
    errorMessage = '服务器内部错误，请稍后重试';
  } else if (error.response?.data?.message) { // 其他HTTP错误
    errorMessage = error.response.data.message;
  } else if (error.code === 'ECONNABORTED') { // 网络超时
    errorMessage = '请求超时，请检查网络连接';
  }

  // 显示错误消息
  if (context.$message) {
    context.$message.error(errorMessage);
  } else if (context.$hideLoading) {
    context.$hideLoading({
      method: 'error',
      tips: errorMessage
    });
  }

  return errorMessage;
}

/**
 * 检查认证状态
 * @param {Object} context - Vue组件上下文
 * @returns {boolean} - 是否已认证
 */
export function checkAuthStatus(context) {
  // 检查多种可能的token存储方式
  const token = context.$cookies?.get('sqlreview_token') ||
               context.$cookies?.get('lu_token') ||
               document.cookie.includes('sqlreview_token') ||
               document.cookie.includes('lu_token');

  return !!token;
}

/**
 * 等待认证完成的工具函数
 * @param {Object} context - Vue组件上下文
 * @param {Function} callback - 认证完成后的回调函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} retryInterval - 重试间隔（毫秒）
 */
export function waitForAuth(context, callback, maxRetries = 10, retryInterval = 1000) {
  let retryCount = 0;

  const checkAuth = () => {
    if (checkAuthStatus(context)) {
      callback();
    } else if (retryCount < maxRetries) {
      retryCount++;
      console.warn(`等待认证完成... (${retryCount}/${maxRetries})`);
      setTimeout(checkAuth, retryInterval);
    } else {
      console.error('认证超时，请手动刷新页面或重新登录');
      if (context.$message) {
        context.$message.error('认证超时，请刷新页面或重新登录');
      }
    }
  };

  checkAuth();
}

/**
 * 创建带有错误处理的API请求包装器
 * @param {Object} context - Vue组件上下文
 * @param {Function} apiFunction - API请求函数
 * @param {string} loadingProperty - 加载状态属性名
 * @param {string} errorMessage - 错误消息
 */
export function createApiWrapper(context, apiFunction, loadingProperty, errorMessage) {
  return async (...args) => {
    if (loadingProperty) {
      context[loadingProperty] = true;
    }

    try {
      const result = await apiFunction(...args);
      return result;
    } catch (error) {
      handleApiError(error, context, errorMessage);
      throw error;
    } finally {
      if (loadingProperty) {
        context[loadingProperty] = false;
      }
    }
  };
}

export default {
  handleApiError,
  checkAuthStatus,
  waitForAuth,
  createApiWrapper
};
