# DataView 组件重构说明

## 概述

将原有的下拉选择项目的方式改为卡片选择的方式，提升用户体验。

## 组件结构

```
DataView.vue (新增)
├── 项目选择区域 (卡片布局)
└── Index.vue (原工作台内容)
    └── Todo.vue (修改后的待办事项组件)
```

## 主要变更

### 1. DataView.vue (新增)
- **功能**: 项目选择的父组件
- **布局**: Row + Col + Card，一行4个卡片
- **数据源**: 复用 `getProjectParams()` 的 API 接口
- **分组逻辑**:
  - 有子项目的按父项目分组
  - 无子项目的归为"其它"分组

### 2. index.vue (修改)
- **新增 props**: `selectedProject`, `selectedDataSource`
- **新增事件**: `@data-source-change`
- **功能**: 接收父组件选中的项目和数据源

### 3. Todo.vue (修改)
- **移除**: 项目选择下拉框
- **保留**: 数据源选择和时间选择
- **新增 props**: `selectedProject`, `selectedDataSource`
- **新增计算属性**: `currentDataSourceId`, `currentTreeId`
- **新增监听器**: 监听父组件传递的参数变化

## 布局说明

### 有子项目的分组
```
Row: [项目组名称]                    [查看全部 →]
     [卡片1] [卡片2] [卡片3] [卡片4]
```

### 无子项目的分组
```
Row: [其它]                         
     [卡片1] [卡片2] [卡片3] [卡片4]
```

## 交互逻辑

1. **项目选择**: 点击卡片选中项目，卡片高亮显示
2. **项目跳转**: 点击卡片内的"进入"按钮跳转到项目详情
3. **查看全部**: 点击分组标题右侧的"查看全部"按钮
4. **数据联动**: 选择项目后自动更新数据源选择器
5. **数据刷新**: 选择变化后自动刷新工作台数据

## 响应式设计

- **桌面端**: 4个卡片一行
- **平板端**: 3个卡片一行 (≤1200px)
- **手机端**: 2个卡片一行 (≤768px)
- **小屏手机**: 1个卡片一行 (≤576px)

## API 接口

使用原有的 `/sqlreview/report/report_all_project_group` 接口获取项目数据。

## 样式特点

- **卡片悬停**: 阴影效果 + 向上移动
- **选中状态**: 蓝色边框 + 阴影
- **响应式**: 支持不同屏幕尺寸
- **现代化**: 圆角、阴影、过渡动画
