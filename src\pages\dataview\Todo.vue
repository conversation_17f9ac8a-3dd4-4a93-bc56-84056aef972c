<template>
  <!-- 代办事项 -->
  <div class="quater-block small-size todo-block">
    <div class="search-area">
      <div class="instance">
        <DataBaseChoose
          :value="currentDataSourceId"
          :allowClear="true"
          :dropdownMatchSelectWidth="true"
          optionLabelProp="children"
          v-bind="instanceParams"
          @change="onInstanceChange"
        />
      </div>
      <div class="search-by-time">
        <a-button
          type="default"
          icon="arrow-left"
          @click="handleBackToParent"
          class="back-button"
          v-if="selectedProject"
        >
          返回项目选择
        </a-button>
        <a-select
          v-bind:value="timeType"
          @change="onTimeTypeChange"
          :allowClear="true"
          placeholder="请选择日期周期"
        >
          <a-select-option value="DAY"> 按日查看 </a-select-option>
          <a-select-option value="WEEK"> 按周查看 </a-select-option>
          <a-select-option value="MONTH"> 按月查看 </a-select-option>
        </a-select>
        <a-date-picker
          @change="onDataChange"
          v-if="timeType == 'DAY'"
          v-model="timePicker"
          :disabledDate="disabledDate"
        />
        <a-week-picker
          @change="onDataChange"
          placeholder="请选择周"
          v-if="timeType == 'WEEK'"
          v-model="timePicker"
          :disabledDate="disabledWeek"
        />
        <a-month-picker
          @change="onDataChange"
          placeholder="请选择月份"
          v-if="timeType == 'MONTH'"
          v-model="timePicker"
          :disabledDate="disabledMonth"
        />
      </div>
    </div>
    <div class="to-do-list">
      <a-skeleton :loading="loading" active>
        <div class="title">待办事项</div>
        <div
          :class="[
            'events',
            'developer' == role && 'developer-events',
            'leader' == role && 'leader-events'
          ]"
        >
          <!-- admin, dba, leader有 -->
          <div
            @click="skip('orderList', 'toDo')"
            v-if="['admin', 'dba', 'leader'].includes(role)"
          >
            <custom-icon type="arrow-right"></custom-icon>
            <div>
              <span>{{ taskData.daiban || 0 }}</span> <span>待审工单</span>
            </div>
          </div>
          <div
            @click="skip('orderList', 'urge')"
            v-if="['admin', 'dba', 'leader'].includes(role)"
          >
            <custom-icon type="arrow-right"></custom-icon>
            <div>
              <span>{{ taskData.cuiban || 0 }}</span>
              <span>催办工单</span>
            </div>
          </div>
          <!-- developer有 -->
          <div
            @click="skip('project-review', 'audit')"
            v-if="'developer' == role"
          >
            <custom-icon type="arrow-right"></custom-icon>
            <div>
              <span>{{ taskData.to_be_reviewed || 0 }}</span>
              <span>待提审工单</span>
            </div>
          </div>
          <div
            @click="skip('white-list')"
            v-if="['admin', 'dba'].includes(role)"
          >
            <custom-icon type="arrow-right"></custom-icon>
            <div>
              <span><custom-icon type="lu-icon-whitelist"></custom-icon></span>
              <span>白名单管理</span>
            </div>
          </div>
          <!-- admin, dba有 -->
          <!-- <div
            @click="skip('home-whitelist')"
            v-if="['admin', 'dba'].includes(role)"
          >
            <custom-icon type="arrow-right"></custom-icon>
            <div>
              <span>{{ taskData.daichulisql || 0 }}</span>
              <span>待审白名单</span>
            </div>
          </div> -->
        </div>
        <div class="skip">
          <div
            v-for="(item, index) in skipMap"
            :key="index"
            @click="skip(item.route)"
          >
            <span>
              <custom-icon :type="item.icon"></custom-icon>
            </span>
            <span>{{ item.name }}</span>
          </div>
        </div>
      </a-skeleton>
    </div>
  </div>
</template>

<script>
import InstanceItem from '@/components/Biz/InstanceItem';
import moment from 'moment';
import common from '@/utils/common';
import { exportIndexExecl } from '@/api/dataview';
export default {
  components: { InstanceItem },
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    taskData: {
      type: Object,
      default: () => {}
    },
    skipMap: {
      type: Array,
      default: () => []
    },
    selectedProject: {
      type: [String, Number],
      default: null
    },
    selectedDataSource: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    let role = _.get(this.$store, 'state.account.user.role');
    // roleArr = ['leader', 'admin', 'dba', 'developer'];
    return {
      role,
      dataSourceId: undefined,
      timeType: undefined,
      treeId: undefined,
      timePicker: undefined,
      timeArr: [],
      timeValue: null,
      instanceList: [],
      instanceParams: this.getInstanceParams(),
      disabledDate: current => {
        return current >= moment().startOf('day');
      },
      disabledWeek: current => {
        // return current >= moment().startOf('week');
        return current > moment().startOf('day');
      },
      disabledMonth: current => {
        // return current >= moment().startOf('month');
        return current > moment();
      }
    };
  },
  computed: {
    // 当前选中的数据源ID
    currentDataSourceId() {
      return this.selectedDataSource || this.dataSourceId;
    },

    // 当前选中的项目ID
    currentTreeId() {
      return this.selectedProject || this.treeId;
    }
  },
  watch: {
    // 监听父组件传递的项目变化
    selectedProject: {
      handler(newVal) {
        if (newVal !== this.treeId) {
          this.treeId = newVal;
          this.dataSourceId = undefined; // 重置数据源
          this.updateInstanceParams();
        }
      },
      immediate: true
    },

    // 监听父组件传递的数据源变化
    selectedDataSource: {
      handler(newVal) {
        if (newVal !== this.dataSourceId) {
          this.dataSourceId = newVal;
        }
      },
      immediate: true
    }
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    getInstanceParams(params) {
      return {
        url: `/sqlreview/report/report_all_datasource`,
        reqParams: params,
        method: 'post',
        mode: 'default',
        placeholder: '请选择数据源',
        allowClear: false,
        backSearch: true,
        allowSearch: true,
        dropdownMatchSelectWidth: false,
        getPopupContainer: () => {
          return document.body;
        },
        loaded: (data = []) => {
          this.instanceList = data.map(item => {
            return {
              ...item,
              db_type: item.db_type,
              instance_usage: item.env,
              showText: item.label,
              isNeedTips: false,
              label: item.label + `(${item.db_url})`
            };
          });
        },
        beforeLoaded(data) {
          return data.map(item => {
            return {
              ...item,
              db_type: item.db_type,
              instance_usage: item.env,
              showText: item.label,
              isNeedTips: false,
              label: item.label + `(${item.db_url})`
            };
          });
        }
      };
    },
    // 更新数据源参数
    updateInstanceParams() {
      const params = this.currentTreeId ? { tree_id: this.currentTreeId } : {};
      this.$set(this, 'instanceParams', this.getInstanceParams(params));
    },

    // sort by 数据源
    onInstanceChange(val) {
      this.dataSourceId = val;
      this.$emit('data-source-change', val);
      this.$emit('refresh', this.currentTreeId, val);
    },
    // sort by 数据源 时间类型 day week month
    onTimeTypeChange(e) {
      this.timeType = e;
      // const startTime = moment()
      //   .subtract(1, e)
      //   .format('YYYY-MM-DD HH:mm:ss');
      // const endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      // this.timeValue = [
      //   moment(startTime, 'YYYY-MM-DD HH:mm:ss'),
      //   moment(endTime, 'YYYY-MM-DD HH:mm:ss')
      // ];
      // this.timeArr = [startTime, endTime];
      this.timePicker = e
        ? moment()
            .subtract(1, e)
            .format('YYYY-MM-DD HH:mm:ss')
        : undefined;
      this.$emit(
        'refresh',
        this.treeId,
        this.dataSourceId,
        this.timeType,
        this.timePicker
      );
    },
    // sort by 时间
    // onDataPickerChange(value) {
    //   this.timeValue = value;
    //   this.timeType = undefined;
    //   this.timeArr = value.map(time => time.format('YYYY-MM-DD HH:mm:ss'));
    //   this.$emit(
    //     'refresh',
    //     this.treeId,
    //     this.dataSourceId,
    //     this.timeArr
    //   );
    // },
    onDataChange(value) {
      this.timePicker = value.format('YYYY-MM-DD HH:mm:ss');
      this.$emit(
        'refresh',
        this.treeId,
        this.dataSourceId,
        this.timeType,
        this.timePicker
      );
    },

    // 返回父级卡片选择
    handleBackToParent() {
      this.$emit('back-to-parent');
    },
    skip(name, key) {
      if (name == 'export') {
        this.$showLoading({
          tips: `下载中...`
        });
        const params = {
          tree_id: this.treeId,
          data_source_id: this.dataSourceId,
          type: this.timeType,
          date: this.timePicker
        };
        exportIndexExecl(params)
          .then(res => {
            common.downLoad(this, res);
          })
          .catch(e => {
            console.error(e);
            this.$hideLoading({
              method: 'error',
              tips: _.get(e || {}, 'response.data.message') || '请求失败'
            });
          });
        return;
      }
      if (key == 'toDo') {
        this.$router.push({ name, query: { dba_status: '1' } });
        return;
      }
      if (key == 'audit') {
        this.$router.push({ name, query: { dba_status: '0', status: '-1,2' } });
        return;
      }
      if (key == 'urge') {
        this.$router.push({ name, query: { is_urge: 1 } });
        return;
      }
      this.$router.push({ name });
    }
  }
};
</script>

<style lang="less" scoped>
.todo-block {
  height: 420px;
  width: 25%;
  .title {
    font-size: 16px;
    color: #1f1f1f;
    padding-bottom: 16px;
    font-weight: bold;
  }
  border-radius: 0;
  box-shadow: none;
  box-shadow: none;
  background: transparent;
  padding: 0;
  margin-right: 0;
  /deep/.search-area {
    border-radius: 8px;
    box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.05);
    height: 158px;
    margin-bottom: 12px;
    background: #ffffff;
    width: 100%;
    padding: 14px 24px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .search-by-project,
    .search-by-time {
      display: flex;
      align-items: center;
      .ant-select {
        width: 100%;
        border-radius: 8px;
        .ant-select-selection--single {
          border: none;
          background: #f5f5f5;
        }
      }
    }
    .instance {
      margin: 12px 0;
      background: #f5f5f5;
      width: 100%;
      border-radius: 8px;
      position: relative;
      height: 32px;

      .biz-instance-item {
        // display: inline-block;
        width: 80%;
        .instance-item-tag {
          background: #f2f2f2;
          border: 0;
          height: 32px;

          &::after {
            display: none;
          }

          .database-image {
            margin-left: 36px;
            > span > .custom-icon {
              margin-right: 0;
            }
            > span > .iconText {
              overflow: hidden;
              max-width: 180px;
              > pre {
                font-size: 13px;
                color: #71717a;
                font-weight: 400;
                white-space: nowrap;
                font-family: 'PingFang SC', 'Microsoft YaHei';
              }
            }
          }
        }

        &:hover {
          .iconText {
            color: @primary-color;
            font-weight: 500;
          }
        }
      }
      .biz-data-base-choose {
        > .ant-select-selection {
          height: 32px;
          border: none;
          background: #f5f5f5;
          .ant-select-selection__rendered {
            margin: 0;
            .ant-select-selection__placeholder {
              margin-left: 11px;
            }
          }
        }
      }
    }
    .search-by-time {
      .back-button {
        margin-right: 12px;
        border-radius: 6px;
        border-color: #d9d9d9;
        color: #595959;

        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
        }

        &:focus {
          border-color: #40a9ff;
          color: #40a9ff;
        }
      }

      .ant-select {
        width: 50%;
        min-width: 100px;
        margin-right: 8px;
        .ant-select-selection__rendered {
          line-height: 34px;
          .ant-select-selection-selected-value {
            color: rgba(0, 0, 0, 0.85);
          }
        }
        .ant-select-arrow {
          margin-top: -4px;
        }
      }
      .ant-calendar-picker {
        border-radius: 8px;
        .ant-calendar-picker-input {
          border: none;
          background: #f5f5f5;
        }
      }
    }
  }
  .to-do-list {
    padding: 12px 24px;
    height: 250px;
    background: #ffffff;
    width: 100%;
    border-radius: 8px;
    box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.05);
    .title {
      font-size: 16px;
      color: #1f1f1f;
      font-weight: 600;
      padding-bottom: 12px;
    }
    .events {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      &.developer-events {
        justify-content: center;
        > div {
          flex-grow: 0.8;
        }
      }
      &.leader-events {
        > div {
          flex-grow: 0.4;
        }
      }
      > div {
        background: #ffffff;
        width: 32%;
        height: 56px;
        border: 1px solid rgba(217, 217, 217, 1);
        border-radius: 4px;
        position: relative;
        .anticon {
          display: none;
          color: #008adc;
          font-size: 12px;
        }
        > div {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .anticon {
            font-size: 20px;
            color: #008adc;
            text-align: center;
            line-height: 26px;
            font-weight: 500;
            display: inline-block;
          }
          span:first-child {
            font-size: 20px;
            color: #008adc;
            text-align: center;
            line-height: 26px;
            font-weight: 500;
          }
          span:last-child {
            color: #595959;
          }
        }
        &:hover {
          cursor: pointer;
          border-color: #008adc;
          .anticon {
            display: inline-block;
            position: absolute;
            top: 4px;
            right: 4px;
          }
          > div {
            .anticon {
              position: initial;
            }
          }
        }
      }
    }
    .skip {
      display: flex;
      flex-wrap: wrap;
      > div {
        width: 33%;
        display: flex;

        // flex-direction: column;
        align-items: center;
        // justify-content: center;
        margin-bottom: 12px;
        padding: 4px 8px;
        border: 1px solid #fff;
        span:first-child {
          width: 18px;
          height: 18px;
          border: 1px solid rgba(163, 234, 255, 1);
          border-radius: 6px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 4px;
          .anticon {
            font-size: 12px;
            color: #008adc;
          }
          // margin-bottom: 8px;
        }
        span:last-child {
          font-size: 12px;
          color: #595959;
          white-space: nowrap;
        }
        &:hover {
          cursor: pointer;
          border: 1px solid rgba(0, 138, 220, 1);
          border-radius: 6px;
          // span:first-child {
          //   border-color: #4096ff;
          //   .anticon {
          //     color: #4096ff;
          //   }
          // }
        }
      }
    }
  }
}
</style>
